import requests
from pydantic import BaseModel
import concurrent.futures


def chat(apikey, model, messages, temperature):
    # 配置API基础URL和密钥
    url = "https://api.narratoai.cn/v1/chat/completions"
    # url = "http://127.0.0.1:7001/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {apikey}"  # 替换为您的API密钥
    }

    class CalendarEvent(BaseModel):
        timestamp: str
        text: str

    # 准备请求数据
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature
    }

    # 发送请求
    response = requests.post(url, headers=headers, json=payload)
    if response.status_code != 200:
        raise Exception(f"状态码：{response.status_code}，响应内容：{response.text}")

    # 返回响应
    return {
        "content": response.json()['choices'][0]['message']['content'],
        "total_tokens": response.json()['usage']['total_tokens']
    }


def chat_v2(apikey, model, messages, temperature):
    from openai import OpenAI

    client = OpenAI(
        api_key=apikey,
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
    )

    response = client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature
    )

    # 返回响应
    return {
        "content": response.choices[0].message.content,
        "total_tokens": response.usage.total_tokens
    }


if __name__ == "__main__":
    messages = [
        {"role": "system",
         "content": "你是一个精通自然语言处理和字幕分析的AI助手，特别强调执行指令的精确性和字面性。"},
        {"role": "user", "content": """
    # 任务

    你的核心任务是分析用户提供的SRT字幕文件内容，在其中**绝对精准地查找**用户指定的中文词语。这意味着只有当字幕文本**字面上、逐字地包含**目标词语时，才算作匹配。在找到精准匹配的字幕段后，你需要结合其上下文理解该词语的真实含义，并尝试判断说话人的一致性。最终，你需要以JSON格式输出所有**严格符合匹配要求**的结果。

    # 输入

    1.  **SRT字幕内容 (srt_content)**: 一个包含完整SRT字幕格式的文本字符串。
    2.  **目标中文词语 (target_word)**: 用户希望在字幕中查找的特定中文词语。
    3.  **上下文窗口大小 (context_window_size)**: 一个整数，表示在找到目标词语的字幕段前后，分别取多少条字幕作为上下文。

    # 处理要求

    1.  **SRT解析**: 正确解析SRT格式，提取每条字幕的序号、时间戳和文本内容。

    2.  **核心匹配原则：字面包含 (CRUCIAL & NON-NEGOTIABLE)**:
        *   **首要步骤**: 对于SRT中的每一条字幕文本，**首先且必须**检查该文本是否**字面上、逐字地、完整地包含**用户提供的 `target_word`。
        *   **严格过滤**: **只有当且仅当**字幕文本**确实包含** `target_word` 时，该字幕段才能被视为一个有效的"匹配字幕段 (matched_segment)"。
        *   **排除**: 如果字幕文本虽然语义相关，但**没有字面上包含** `target_word`，则**绝对不能**将该字幕段视为匹配项，**不得**将其纳入后续分析或最终输出。

    3.  **上下文提取 (仅对字面匹配的字幕段进行)**: 对于每个**成功通过上述字面匹配校验**的字幕段：
        *   记录该字幕段本身。
        *   根据 `context_window_size` 提取其前N条字幕作为"前文 (context_before)"。
        *   根据 `context_window_size` 提取其后N条字幕作为"后文 (context_after)"。

    4.  **语义理解 (仅对字面匹配的字幕段进行)**: 分析 `target_word` 在其所在字幕段及上下文中的具体含义。

    5.  **说话人判断 (尝试性，仅对字面匹配的字幕段进行)**: 基于对话的连贯性、内容主题的延续性等线索，尝试判断包含 `target_word` 的字幕段及其直接上下文是否可能为同一说话人。说明判断依据。

    6.  **最终输出校验**: 在生成JSON输出前，**再次确认**所有 `matched_segment` 中的 `text` 字段都**明确无误地包含** `target_word`。这是最后的防线。

    # 输出格式 (JSON)

    请将所有找到的、**严格符合字面匹配要求**的匹配结果组织成一个JSON数组。数组中的每个元素是一个对象，结构如下：

    ```json
    [
      {
        "target_word": "目标中文词语",
        "matched_segment": { // 此处 text 字段必须包含 target_word
          "index": "字幕序号 (字符串)",
          "timestamp": "时间戳 (字符串)",
          "text": "包含目标词语的字幕文本 (字符串)"
        },
        "context_before": [
          // ...
        ],
        "context_after": [
          // ...
        ],
        "semantic_analysis": "对目标词语在此上下文中真实含义的分析描述 (字符串)",
        "speaker_consistency_analysis": {
            "assessment": "可能同一说话人 / 可能不同说话人 / 难以判断 (字符串)",
            "reasoning": "支持该判断的理由 (字符串)"
        }
      }
      // ... 其他严格匹配项
    ]
    ```
    如果未找到任何**字面上包含** `target_word` 的字幕段，请返回一个空数组 `[]`。

    # 示例（假设 `target_word` 为 "大模型", `context_window_size` 为 1）

    **输入 (srt_content):**
    ```srt
    1
    00:00:01,000 --> 00:00:03,500
    大家好，今天我们聊聊人工智能。

    2
    00:00:04,000 --> 00:00:06,800
    特别是关于"大模型"的最新进展。

    3
    00:00:07,200 --> 00:00:09,500
    这个AI模型能力很强。

    4
    00:00:10,000 --> 00:00:12,000
    它能解决很多复杂问题。
    ```

    **预期输出 (JSON):**
    ```json
    [
      {
        "target_word": "大模型",
        "matched_segment": {
          "index": "2",
          "timestamp": "00:00:04,000 --> 00:00:06,800",
          "text": "特别是关于"大模型"的最新进展。" // 明确包含 "大模型"
        },
        "context_before": [
          {
            "index": "1",
            "timestamp": "00:00:01,000 --> 00:00:03,500",
            "text": "大家好，今天我们聊聊人工智能。"
          }
        ],
        "context_after": [
          {
            "index": "3",
            "timestamp": "00:00:07,200 --> 00:00:09,500",
            "text": "这个AI模型能力很强。"
          }
        ],
        "semantic_analysis": "在此处，"大模型"特指具有大规模参数的先进人工智能模型，讨论的是其技术发展情况。",
        "speaker_consistency_analysis": {
            "assessment": "可能同一说话人",
            "reasoning": "字幕2是对字幕1引入话题的细化，内容连贯，表述方式一致。"
        }
      }
      // 注意：字幕3虽然语义上和"大模型"相关，但因字面上不包含"大模型"，所以不应出现在结果中。
    ]
    ```

    # 开始执行

    请根据以下提供的实际输入执行任务：

    **SRT字幕内容 (srt_content):**
    ```srt
    %s
    ```

    **目标中文词语 (target_word):**
    %s

    **上下文窗口大小 (context_window_size):**
    2

    注意不要输出除结果以外的任何其他字符串！
    """}
    ]
    print(chat_v2("AIzaSyB9nxsAvlZ1FKtPVoG8YJATht6cCTVMBC4", "gemini-2.0-flash", messages, 0.7))

    # 使用 10 个并发线程，同步使用 chat
    # def run_chat(idx):
    #     # 这里可以根据需要为每个线程定制不同的 messages 或参数
    #     return chat("123456789", "gemini-2.0-flash", messages, 0.7)
    #
    # results = []
    # with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
    #     future_to_idx = {executor.submit(run_chat, i): i for i in range(20)}
    #     for future in concurrent.futures.as_completed(future_to_idx):
    #         idx = future_to_idx[future]
    #         try:
    #             result = future.result()
    #             print(f"线程{idx}结果: {result}")
    #             results.append(result)
    #         except Exception as exc:
    #             print(f"线程{idx}发生异常: {exc}")
    #
    #