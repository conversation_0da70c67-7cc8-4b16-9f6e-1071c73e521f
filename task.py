import os
import json
import datetime
import time
import shutil
from loguru import logger

from app.services.step1_rewrite_dialogue import assemble_subtitles, rewrite_dialogue
from app.services.step2_clip_video import clip_video
from app.services.step3_extract_audio import extract_audio_from_videos
from app.utils.clone_quality_check import check_audio_quality
from app.services.step4_audio_clone import voice_clone
from app.services.step5_replace_audio import replace_word_in_audio
from app.services.step6_merge_video_audio_subtitles import batch_process_videos_from_json
from app.services.step7_merge_videos import merge_videos, check_hardware_acceleration
from app.services.step8_output import merge_video_materials


def run_task(step1_output_path: str, work_dir: str, tts_url: str, quality_mode: str = 'high_quality_ignore'):
    """
    总任务

    参数:
        vocabulary_path: 词汇表路径
        srt_en_path_list: 英文字幕路径列表
        srt_zh_path_list: 中文字幕路径列表
        work_dir: 工作目录
        quality_mode: 质量模式，可选值为 'high_quality_ignore'高质量忽略 或 'fail_replace'失败替换, 'no_ignore_no_replace'不忽略不替换
    """

    logger.info('1. 开始执行单词统计')
    """
    1. 执行重写
    """
    logger.info('1. 使用 gemini 开始重写台词')
    apikey = "sk-narra-ZPA07ethehTb2VQ5mMI1P8tt"
    model = "gemini-2.0-flash"
    temperature = 0.7
    step2_output_path = os.path.join(work_dir, 'step2_rewrite.json')
    if not os.path.exists(step2_output_path):
        with open(step1_output_path, "r", encoding="utf-8") as f:
            step1_all = json.load(f)
        # step1_data = [item for item in step1_all if item['word'] == word]
        srt = assemble_subtitles(step1_all[0])

        rewrites = []
        for item in srt:
            word = item['word']
            srt_line = item['text']
            video_name = item['video']
            target_srt = item['subtitles'][3]
            word_zh = item['translations'][0].split('.')[1].strip()
            try:
                # 使用 gemini 进行重写
                res = rewrite_dialogue(apikey, model, temperature, video_name, srt_line, word, target_srt)
                res["text"] = res["origin"].replace(word_zh, f" {word} ")
                res["word_zh"] = word_zh
                res["word_en"] = item['word']
                res["video"] = item['video']
                rewrites.append(res)

                # # 直接插入目标段落  '00:02:13,280 --> 00:02:14,750
                # start = item['subtitles'][2].split('\n')[0].split(' --> ')[0]
                # end = item['subtitles'][4].split('\n')[0].split(' --> ')[1]
                # origin1 = item['subtitles'][2].split('\n')[1]
                # origin2 = item['subtitles'][3].split('\n')[1]
                # origin3 = item['subtitles'][4].split('\n')[1]
                # word_zh = item['translations'][0].split('.')[1].strip()
                # res = {
                #     "timestamp": f"{start} --> {end}",
                #     "text": f"{origin1} {origin2} {origin3}".replace(word_zh, f" {item['word']} "),
                #     "origin": f"{origin1} {origin2} {origin3}",
                #     "word_zh": word_zh,
                #     "word_en": item['word'],
                #     "video": item['video']
                # }
                # rewrites.append(res)
            except Exception as e:
                logger.warning(f"处理字幕失败: {e}")
                continue

        # 原始重写结果
        with open(os.path.join(work_dir, 'step1_rewrite.json'), "w", encoding='utf-8') as f:
            json.dump(rewrites, f, ensure_ascii=False, indent=2)
        # 将提取出的元素写入新的 JSON 文件
        with open(step2_output_path, "w", encoding='utf-8') as f:
            json.dump(rewrites, f, ensure_ascii=False, indent=2)
    logger.success('台词重写完成')

    """
    2. 执行视频裁剪
    """
    logger.info('2. 开始执行视频裁剪')
    # 视频所在目录
    video_dir = os.path.join(os.getcwd(), 'resources', 'video')
    clip_output_dir = os.path.join(work_dir, 'step2_video_clips')
    # 输出目录
    os.makedirs(clip_output_dir, exist_ok=True)

    # 判断视频目录是否为空
    if not os.listdir(clip_output_dir):
        # 从重写JSON中获取时间戳信息
        with open(step2_output_path, 'r', encoding='utf-8') as f:
            scripts = json.load(f)

        for script in scripts:
            video_name = f"{script['video']}.mp4"
            timestamp = script['timestamp']
            logger.debug(f"{video_name}: {timestamp}")

            # 构建完整视频路径
            video_path = os.path.join(video_dir, video_name)

            # 如果视频文件存在，执行裁剪
            if os.path.exists(video_path):
                output = clip_video(video_path, timestamp, clip_output_dir)
                if output:
                    script['clip_video'] = output
                    logger.debug(f"裁剪成功: {output}")
                # 更新JSON文件
                with open(step2_output_path, 'w', encoding='utf-8') as f:
                    json.dump(scripts, f, ensure_ascii=False, indent=4)
            else:
                logger.warning(f"视频文件不存在: {video_path}")
    logger.success('视频裁剪完成')

    """
    3. 提取音频
    """
    logger.info('3. 开始提取音频')
    # 裁剪后的视频目录
    audio_output_dir = os.path.join(work_dir, 'step3_audio')
    os.makedirs(audio_output_dir, exist_ok=True)

    # 提取音频
    extract_audio_from_videos(clip_output_dir, audio_output_dir, audio_format='wav')

    # 更新JSON文件，添加音频路径
    with open(step2_output_path, 'r', encoding='utf-8') as f:
        scripts = json.load(f)

    for script in scripts:
        if 'clip_video' in script:
            video_filename = os.path.basename(script['clip_video'])
            video_name = os.path.splitext(video_filename)[0]
            audio_path = os.path.join(audio_output_dir, f"{video_name}.wav")
            if os.path.exists(audio_path):
                script['audio_path'] = audio_path

    with open(step2_output_path, 'w', encoding='utf-8') as f:
        json.dump(scripts, f, ensure_ascii=False, indent=4)
    logger.success('音频提取完成')

    """
    4. 音频克隆与质量检查（合并步骤4和5）
    """
    logger.info('4. 开始音频克隆与质量检查')
    # 设置音频克隆服务器地址
    server_url = tts_url  # 确保Spark-TTS API服务已运行
    # server_url = "http://***********:8000/voice_clone/"  # 确保Spark-TTS API服务已运行
    # server_url = "http://127.0.0.1:8000/voice_clone/"  # 确保Spark-TTS API服务已运行

    # 创建音频克隆输出目录
    clone_output_dir = os.path.join(work_dir, 'step4_cloned_audio')
    os.makedirs(clone_output_dir, exist_ok=True)

    if not os.listdir(clone_output_dir):
        # 读取重写脚本
        with open(step2_output_path, 'r', encoding='utf-8') as f:
            scripts = json.load(f)

        # 创建一个新列表，用于存储成功克隆和质量检查通过的脚本
        valid_scripts = []

        for script in scripts:
            if 'audio_path' not in script or not os.path.exists(script['audio_path']):
                logger.warning(f"缺少音频文件: {script.get('audio_path', 'None')}")
                continue

            text = script["word_en"]  # 需要 clone 的单词
            origin_text = script.get("origin", "")  # 原始文本作为提示
            audio_file_path = script["audio_path"]

            logger.info(f"处理单词: {text}, 音频文件: {os.path.basename(audio_file_path)}")

            # 最多尝试3次克隆和质量检查
            max_clone_attempts = 3
            clone_success = False

            for attempt in range(1, max_clone_attempts + 1):
                logger.info(f"尝试 {attempt}/{max_clone_attempts} 克隆单词: {text}")

                # voice_clone 函数只负责获取音频数据，不负责存储
                result = voice_clone(text, audio_file_path, server_url, prompt_text=origin_text, max_retries=5)

                if result.get("success") and "audio_data" in result:
                    # 生成音频文件名，添加尝试次数标记
                    audio_filename = f"cloned_{os.path.basename(audio_file_path)}"
                    if attempt > 1:
                        base_name, ext = os.path.splitext(audio_filename)
                        audio_filename = f"{base_name}_attempt{attempt}{ext}"

                    # 构建完整的音频文件路径
                    local_audio_path = os.path.join(clone_output_dir, audio_filename)

                    # 在 task.py 中负责将二进制数据保存到文件
                    with open(local_audio_path, 'wb') as f:
                        f.write(result["audio_data"])

                    logger.debug(f"音频克隆成功，保存至: {local_audio_path}")

                    # 立即进行质量检查
                    logger.info(f"对克隆音频进行质量检查: {os.path.basename(local_audio_path)}")
                    check_result, details = check_audio_quality(local_audio_path, text)

                    if check_result.value == '匹配':
                        logger.info(f"质量检查通过: {os.path.basename(local_audio_path)}")
                        # 更新脚本中的音频路径
                        script["cloned_audio_path"] = local_audio_path

                        # 如果是有道API生成的，记录来源
                        if result.get("source") == "youdao":
                            script["audio_source"] = "youdao"

                        # 标记成功并跳出重试循环
                        clone_success = True
                        break
                    else:
                        logger.warning(f"质量检查不通过 (尝试 {attempt}/{max_clone_attempts}): {check_result.value}")
                        # 如果不是最后一次尝试，继续下一次
                        if attempt < max_clone_attempts:
                            logger.info(f"将进行第 {attempt+1} 次尝试...")
                            time.sleep(2)  # 短暂等待后重试
                else:
                    logger.warning(f"音频克隆失败 (尝试 {attempt}/{max_clone_attempts}): {result.get('error', '未知错误')}")
                    # 如果不是最后一次尝试，继续下一次
                    if attempt < max_clone_attempts:
                        logger.info(f"将进行第 {attempt+1} 次尝试...")
                        time.sleep(2)  # 短暂等待后重试

            # 如果所有尝试都失败，记录并跳过此脚本
            if not clone_success:
                logger.error(f"单词 '{text}' 在 {max_clone_attempts} 次尝试后仍未成功克隆或质量检查不通过，将从列表中移除")
            else:
                # 只保留成功的脚本
                valid_scripts.append(script)

            # 在处理下一个脚本前稍作等待
            time.sleep(3)  # 防止请求过于频繁

        # 使用有效脚本更新原始脚本列表
        scripts = valid_scripts

        # 更新JSON文件
        with open(step2_output_path, 'w', encoding='utf-8') as f:
            json.dump(scripts, f, ensure_ascii=False, indent=4)

    logger.success('音频克隆与质量检查完成')

    """
    5. 替换音频
    """
    logger.info('5. 开始替换音频')
    # 创建替换后音频的输出目录
    replace_output_dir = os.path.join(work_dir, 'step5_replaced_audio')
    os.makedirs(replace_output_dir, exist_ok=True)
    if not os.listdir(replace_output_dir):
        # Azure语音识别API密钥，用于对齐单词时间
        azure_key = '2vVtzA7OCTbkHLqMjvnNIRMIabVQCzOM1qrzuNmhsb5nSg5mTiCOJQQJ99BEACYeBjFXJ3w3AAAYACOGzHUN'

        # 读取重写脚本
        with open(step2_output_path, 'r', encoding='utf-8') as f:
            scripts = json.load(f)

        for script in scripts:
            if 'audio_path' not in script or 'cloned_audio_path' not in script:
                logger.warning("缺少原始音频或克隆音频路径")
                continue

            if not os.path.exists(script['audio_path']) or not os.path.exists(script['cloned_audio_path']):
                logger.warning(f"音频文件不存在: 原始音频={script.get('audio_path')}, 克隆音频={script.get('cloned_audio_path')}")
                continue

            original_audio_path = script['audio_path']
            replacement_audio_path = script['cloned_audio_path']
            target_word = script['word_zh']  # 要替换的单词
            offset_adjustment_ms = -50  # 时间偏移调整，可以根据需要修改

            # 输出文件路径
            output_filename = f"replaced_{os.path.basename(original_audio_path)}"
            output_path = os.path.join(replace_output_dir, output_filename)

            try:
                # 识别原始音频中的单词
                from app.utils.asr_azure import transcribe_audio_with_azure
                data = transcribe_audio_with_azure(audio_file_path=original_audio_path, azure_key=azure_key)
                script['asr_words'] = data
                # 执行替换
                replaced_audio = replace_word_in_audio(
                    original_audio_path,
                    replacement_audio_path,
                    data["words"],
                    target_word,
                    offset_adjustment_ms
                )

                # 保存替换后的音频
                if replaced_audio is None:
                    logger.warning(f"替换音频失败: {original_audio_path}")
                    continue
                replaced_audio.export(output_path, format="wav")
                logger.debug(f"替换完成，文件已保存至: {output_path}")

                # 更新脚本中的替换音频路径
                script['replace_audio'] = output_path
            except Exception as e:
                logger.error(f"替换音频失败: {e}")

        # 更新JSON文件
        with open(step2_output_path, 'w', encoding='utf-8') as f:
            json.dump(scripts, f, ensure_ascii=False, indent=4)
    logger.success('音频替换完成')
    # return step2_output_path
    """
    6. 合并视频、音频、字幕
    """
    logger.info('6. 开始合并视频、音频和字幕')
    # 创建合并输出目录和临时目录
    merge_output_dir = os.path.join(work_dir, 'step6_merged_videos')
    os.makedirs(merge_output_dir, exist_ok=True)
    if not os.listdir(merge_output_dir):
        temp_dir = os.path.join(merge_output_dir, 'temp')
        os.makedirs(temp_dir, exist_ok=True)

        # 设置字幕参数
        subtitle_settings = {
            "font_size": 28,            # 字体大小
            "font_color": "FFFFFF",     # 字体白色
            "border_color": "000000",   # 描边黑色
            "border_width": 2.0,
            "position": "bottom",
            "bottom_margin": 30,
            "top_margin": 10
        }

        # 从JSON文件批量处理视频
        successful_outputs = batch_process_videos_from_json(
            step2_output_path,
            merge_output_dir,
            temp_dir,
            subtitle_settings
        )

        # 记录成功合并的视频
        merge_result_path = os.path.join(work_dir, 'step6_merge_results.json')
        with open(merge_result_path, 'w', encoding='utf-8') as f:
            json.dump(successful_outputs, f, ensure_ascii=False, indent=4)

        # 更新原始JSON，添加合并后的视频路径
        with open(step2_output_path, 'r', encoding='utf-8') as f:
            scripts = json.load(f)

        # 为每个脚本项添加合并后的视频路径
        for script in scripts:
            if 'clip_video' in script:
                base_name = os.path.splitext(os.path.basename(script['clip_video']))[0]
                merged_video_path = os.path.join(merge_output_dir, f"{base_name}.mp4")
                if os.path.exists(merged_video_path):
                    script['merged_video'] = merged_video_path

        # 更新JSON文件
        with open(step2_output_path, 'w', encoding='utf-8') as f:
            json.dump(scripts, f, ensure_ascii=False, indent=4)

        # 清理临时目录
        shutil.rmtree(temp_dir)

    logger.success('视频、音频和字幕合并完成')

    """ 
    7. 合并所有视频片段
    """
    logger.info('7. 开始合并所有视频片段')
    # 最终视频输出目录
    final_video_dir = os.path.join(work_dir, 'step7_final_video')
    os.makedirs(final_video_dir, exist_ok=True)

    # 最终合并视频路径
    final_video_path = os.path.join(final_video_dir, 'final_merged_video.mp4')
    if not os.path.exists(final_video_path):
        # 读取已处理的视频信息
        with open(step2_output_path, 'r', encoding='utf-8') as f:
            scripts = json.load(f)

        # 收集所有合并后的视频路径
        all_video_paths = []
        for script in scripts:
            if 'merged_video' in script and os.path.exists(script['merged_video']):
                all_video_paths.append(script['merged_video'])

        # 检查是否支持硬件加速
        hw_accel = check_hardware_acceleration()
        if hw_accel:
            logger.debug(f"使用硬件加速编码器: {hw_accel}")
        else:
            logger.debug("不支持硬件加速，使用软编码")

        # 合并所有视频片段
        if all_video_paths:
            merge_videos(all_video_paths, final_video_path, hw_accel)
            logger.debug(f"所有视频片段合并完成: {final_video_path}")
        else:
            logger.warning("没有可合并的视频片段")
    logger.success('视频片段合并完成')

    """
    8. 最终输出处理
    """
    logger.info('8. 开始最终视频处理和导出')
    # 最终成品视频输出目录
    output_final_dir = os.path.join(work_dir, 'step8_output')
    os.makedirs(output_final_dir, exist_ok=True)

    # 最终成品视频路径
    final_output_path = os.path.join(output_final_dir, 'final_output.mp4')

    # 从脚本中获取目标单词信息
    with open(step2_output_path, 'r', encoding='utf-8') as f:
        scripts = json.load(f)

    # 假设我们使用第一个词的信息作为示例
    if scripts:
        text_word = scripts[0]['word_en']  # 目标单词

        # 从词汇表获取单词音标和翻译信息
        with open(step1_output_path, 'r', encoding='utf-8') as f:
            vocabulary = json.load(f)

        # 查找单词信息
        text_us_phonetic = ""
        translations = ""
        for word_item in vocabulary:
            if word_item['word'].lower() == text_word.lower():
                text_us_phonetic = f"/ {word_item.get('uk_phonetic', "null")} /"
                translations = word_item.get('translations', [])[0]
                break

        # 生成最终成品视频
        if os.path.exists(final_video_path):
            logger.debug(f"目标视频: {final_video_path}")
            logger.debug(f"目标单词: {text_word}")
            logger.debug(f"单词音标: {text_us_phonetic}")
            logger.debug(f"单词解释: {translations}")

            output = merge_video_materials(
                video_path=final_video_path,
                text_word=text_word,
                text_us_phonetic=text_us_phonetic,
                translations=translations,
                output_path=final_output_path,
                vertical_mode=True  # 启用竖屏模式
            )
            logger.debug(f"最终视频导出完成: {output}")
        else:
            logger.warning(f"最终视频文件不存在: {final_video_path}")
    else:
        logger.warning("没有可用的单词信息")
    logger.success('最终视频处理导出完成')

    logger.info('所有步骤已完成!')
    return final_output_path


if __name__ == '__main__':
    # 创建工作目录
    # task_name = datetime.datetime.now().strftime('%Y-%m-%d-%H')
    task_name = '2025-05-24-25'
    work_dir = os.path.join(os.getcwd(), 'work', task_name)
    os.makedirs(work_dir, exist_ok=True)

    word_type = 'n'
    word = 'violation'
    step1_output_path = f'resources/word_search_rule_v2/step1_39_n_violation.json'
    # 执行任务
    output_path = run_task(
        step1_output_path=step1_output_path,
        work_dir=work_dir,
        tts_url='http://127.0.0.1:8000/voice_clone/'
    )
    print(f'最终输出视频路径: {output_path}')
