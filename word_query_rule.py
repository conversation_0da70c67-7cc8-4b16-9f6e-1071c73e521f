#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project: tv_en_learning
@File   : 基于中文分词进行强匹配
<AUTHOR> 小林同学
@Date   : 2025/5/20 17:35 

功能说明：
    从 SRT 字幕文件中查找包含指定中文词语的字幕片段，并提供前后上下文

主要函数：
    1. find_word_in_srt(srt_path, word, context_size=3)
       - 在单个SRT文件中查找指定词语
       - 返回包含匹配结果、前3段和后3段上下文的JSON格式数据
    
    2. search_word_in_multiple_srt(srt_dir, word, context_size=3)
       - 在多个SRT文件中批量查找指定词语
    
    3. batch_search_and_save(srt_dir, word, output_dir="resources/word_rule", context_size=3)
       - 批量检索功能：对整个目录下的所有SRT文件进行检索
       - 自动创建输出目录并保存所有结果
       - 为每个有匹配结果的文件生成单独的JSON文件
       - 生成汇总统计文件
    
    4. main_batch_search(word, srt_dir="resources/srt/zh", output_dir="resources/word_rule")
       - 便捷的批量检索主函数，使用简化的参数调用
    
    5. batch_search_and_save_word_rule()
       - 【新增】词汇表批量检索：读取CET6词汇文件，批量检索所有中文翻译
       - 自动整合结果为标准格式，包含英文词汇、音标、词性等完整信息
       - 输出格式与step1_519_adj._conspicuous.json完全一致
    
    6. batch_search_and_save_word_rule_single(word_index, start_index, end_index)
       - 【新增】词汇表分批检索：处理指定范围的词汇，适用于测试或分批处理
    
    7. save_search_result(result, output_path=None)
       - 将搜索结果保存为JSON文件

使用示例：
    # 查找单个文件
    result = find_word_in_srt("resources/srt/zh/S01E01.zho.srt", "能力")
    
    # 批量查找目录中的所有SRT文件（基础版本）
    results = search_word_in_multiple_srt("resources/srt/zh", "能力")
    
    # 【推荐】使用便捷主函数进行批量检索
    main_batch_search("能力")
    
    # 批量检索并自动保存（完整版本）
    stats = batch_search_and_save("resources/srt/zh", "能力")
    
    # 【新增】词汇表批量检索 - 处理所有478个CET6词汇
    batch_search_and_save_word_rule()
    
    # 【新增】词汇表分批检索 - 处理指定范围的词汇
    batch_search_and_save_word_rule_single(word_index=1)  # 只处理第1个词汇
    batch_search_and_save_word_rule_single(start_index=1, end_index=10)  # 处理第1-10个词汇
    
    # 自定义输出目录
    stats = batch_search_and_save("resources/srt/zh", "能力", "custom_output_dir")

批量检索功能特点：
    ✅ 自动创建输出目录 resources/word_rule
    ✅ 为每个有匹配结果的SRT文件生成独立的JSON文件（格式：文件名_词语.json）
    ✅ 生成汇总统计文件（格式：search_summary_词语.json）
    ✅ 实时显示处理进度和统计信息
    ✅ 完整的错误处理，单个文件出错不影响整体处理

输出格式：
    单个文件结果：
    {
        "srt_name": "文件名.srt",
        "srt_path": "完整文件路径",
        "target": [
            {
                "index": 字幕序号,
                "timestamp": "时间戳范围",
                "subtitle": "包含目标词语的字幕文本",
                "context_before": [前面的3段上下文],
                "context_after": [后面的3段上下文]
            }
        ]
    }
    
    汇总统计文件：
    {
        "statistics": {
            "search_word": "能力",
            "srt_directory": "完整源目录路径",
            "output_directory": "完整输出目录路径", 
            "total_files_processed": 37,
            "files_with_matches": 5,
            "total_matches": 12,
            "result_files": ["S01E13_能力.json", "S01E18_能力.json", ...],
            "summary_file": "search_summary_能力.json"
        },
        "all_results": [所有匹配结果的完整数据]
    }
'''

import json
import os
import re
from typing import List, Dict, Optional
import pysrt


def find_word_in_srt(srt_path: str, word: str, context_size: int = 3) -> Dict:
    """
    在SRT字幕文件中查找包含指定中文词语的字幕片段
    
    Args:
        srt_path: SRT文件路径
        word: 要查找的中文词语
        context_size: 前后上下文的字幕数量，默认为3
    
    Returns:
        包含查找结果的字典，格式如下：
        {
            "srt_name": "文件名",
            "srt_path": "完整文件路径", 
            "target": [
                {
                    "index": 字幕序号,
                    "timestamp": "时间戳",
                    "subtitle": "包含目标词语的字幕文本",
                    "context_before": [前面的上下文字幕],
                    "context_after": [后面的上下文字幕]
                }
            ]
        }
    """
    
    # 检查文件是否存在
    if not os.path.exists(srt_path):
        raise FileNotFoundError(f"SRT文件不存在: {srt_path}")
    
    try:
        # 解析SRT文件
        subs = pysrt.open(srt_path, encoding='utf-8')
    except Exception as e:
        try:
            # 尝试其他编码
            subs = pysrt.open(srt_path, encoding='gbk')
        except Exception as e2:
            raise Exception(f"无法解析SRT文件: {e}, {e2}")
    
    # 获取文件名和完整路径
    srt_name = os.path.basename(srt_path)
    full_path = os.path.abspath(srt_path)
    
    # 存储匹配结果
    matches = []
    
    # 遍历所有字幕
    for i, sub in enumerate(subs):
        # 清理字幕文本（去除HTML标签和特殊字符）
        text = re.sub(r'<[^>]+>', '', sub.text)
        text = text.strip()
        
        # 检查是否包含目标词语
        if word in text:
            # 构建上下文
            context_before = []
            context_after = []
            
            # 获取前面的上下文
            start_idx = max(0, i - context_size)
            for j in range(start_idx, i):
                if j < len(subs):
                    before_text = re.sub(r'<[^>]+>', '', subs[j].text).strip()
                    context_before.append({
                        "index": subs[j].index,
                        "timestamp": f"{subs[j].start} --> {subs[j].end}",
                        "text": before_text
                    })
            
            # 获取后面的上下文
            end_idx = min(len(subs), i + context_size + 1)
            for j in range(i + 1, end_idx):
                if j < len(subs):
                    after_text = re.sub(r'<[^>]+>', '', subs[j].text).strip()
                    context_after.append({
                        "index": subs[j].index,
                        "timestamp": f"{subs[j].start} --> {subs[j].end}",
                        "text": after_text
                    })
            
            # 添加到匹配结果
            match_info = {
                "index": sub.index,
                "timestamp": f"{sub.start} --> {sub.end}",
                "subtitle": text,
                "context_before": context_before,
                "context_after": context_after
            }
            matches.append(match_info)
    
    # 构建返回结果
    result = {
        "srt_name": srt_name,
        "srt_path": full_path,
        "target": matches
    }
    
    return result


def save_search_result(result: Dict, output_path: str = None) -> str:
    """
    将搜索结果保存为JSON文件
    
    Args:
        result: 搜索结果字典
        output_path: 输出文件路径，如果为None则自动生成
    
    Returns:
        保存的文件路径
    """
    if output_path is None:
        # 自动生成输出文件名
        srt_name = result["srt_name"]
        base_name = os.path.splitext(srt_name)[0]
        output_path = f"search_result_{base_name}.json"
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    return output_path


def search_word_in_multiple_srt(srt_dir: str, word: str, context_size: int = 3) -> List[Dict]:
    """
    在多个SRT文件中查找指定词语
    
    Args:
        srt_dir: SRT文件目录
        word: 要查找的词语
        context_size: 上下文大小
    
    Returns:
        包含所有匹配结果的列表
    """
    results = []
    
    if not os.path.exists(srt_dir):
        raise FileNotFoundError(f"目录不存在: {srt_dir}")
    
    # 遍历目录中的所有SRT文件
    for filename in os.listdir(srt_dir):
        if filename.lower().endswith('.srt'):
            srt_path = os.path.join(srt_dir, filename)
            try:
                result = find_word_in_srt(srt_path, word, context_size)
                if result["target"]:  # 只添加有匹配结果的文件
                    results.append(result)
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")
                continue
    
    return results


def batch_search_and_save(srt_dir: str, word: str, output_dir: str = "resources/word_rule", context_size: int = 3) -> Dict:
    """
    批量检索SRT文件中的指定词语并保存结果到指定目录
    
    Args:
        srt_dir: SRT文件目录（如：resources/srt/zh）
        word: 要查找的目标中文词语（如："能力"）
        output_dir: 输出目录（默认：resources/word_rule）
        context_size: 上下文大小（默认：3）
    
    Returns:
        包含检索统计信息的字典
        {
            "search_word": "能力",
            "srt_directory": "resources/srt/zh", 
            "output_directory": "resources/word_rule",
            "total_files_processed": 37,
            "files_with_matches": 5,
            "total_matches": 12,
            "result_files": ["S01E13_能力.json", "S01E18_能力.json", ...],
            "summary_file": "search_summary_能力.json"
        }
    """
    
    # 检查输入目录是否存在
    if not os.path.exists(srt_dir):
        raise FileNotFoundError(f"SRT目录不存在: {srt_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 初始化统计信息
    stats = {
        "search_word": word,
        "srt_directory": os.path.abspath(srt_dir),
        "output_directory": os.path.abspath(output_dir),
        "total_files_processed": 0,
        "files_with_matches": 0,
        "total_matches": 0,
        "result_files": [],
        "summary_file": ""
    }
    
    all_results = []
    
    print(f"开始批量检索...")
    print(f"源目录: {srt_dir}")
    print(f"目标词语: {word}")
    print(f"输出目录: {output_dir}")
    print("-" * 50)
    
    # 遍历目录中的所有SRT文件
    srt_files = [f for f in os.listdir(srt_dir) if f.lower().endswith('.srt')]
    stats["total_files_processed"] = len(srt_files)
    
    for filename in srt_files:
        srt_path = os.path.join(srt_dir, filename)
        print(f"正在处理: {filename}")
        
        try:
            result = find_word_in_srt(srt_path, word, context_size)
            
            if result["target"]:  # 如果有匹配结果
                stats["files_with_matches"] += 1
                stats["total_matches"] += len(result["target"])
                all_results.append(result)
                
                # 为每个有结果的文件生成单独的JSON文件
                base_name = os.path.splitext(filename)[0]
                output_filename = f"{base_name}_{word}.json"
                output_path = os.path.join(output_dir, output_filename)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                
                stats["result_files"].append(output_filename)
                print(f"  ✓ 找到 {len(result['target'])} 个匹配项，已保存到: {output_filename}")
            else:
                print(f"  - 未找到匹配项")
                
        except Exception as e:
            print(f"  ✗ 处理失败: {e}")
            continue
    
    # 生成汇总文件
    summary_filename = f"search_summary_{word}.json"
    summary_path = os.path.join(output_dir, summary_filename)
    stats["summary_file"] = summary_filename
    
    summary_data = {
        "statistics": stats,
        "all_results": all_results
    }
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary_data, f, ensure_ascii=False, indent=2)
    
    print("-" * 50)
    print(f"批量检索完成！")
    print(f"总共处理文件: {stats['total_files_processed']}")
    print(f"有匹配结果的文件: {stats['files_with_matches']}")
    print(f"总匹配项数: {stats['total_matches']}")
    print(f"结果文件已保存到: {output_dir}")
    print(f"汇总文件: {summary_filename}")
    
    return stats


def main_batch_search(word: str, srt_dir: str = "resources/srt/zh", output_dir: str = "resources/word_rule"):
    """
    便捷的批量检索主函数
    
    Args:
        word: 要查找的中文词语
        srt_dir: SRT文件目录（默认：resources/srt/zh）
        output_dir: 输出目录（默认：resources/word_rule）
    
    Returns:
        统计信息字典
    
    使用示例：
        # 使用默认路径搜索
        main_batch_search("能力")
        
        # 自定义路径
        main_batch_search("能力", "custom_srt_dir", "custom_output_dir")
    """
    print(f"🔍 开始批量搜索中文词语: '{word}'")
    print(f"📁 SRT文件目录: {srt_dir}")
    print(f"💾 输出目录: {output_dir}")
    print()
    
    try:
        stats = batch_search_and_save(srt_dir, word, output_dir)
        print()
        print("🎉 批量检索任务完成！")
        return stats
    except Exception as e:
        print(f"❌ 批量检索过程中出错: {e}")
        return None


def batch_search_and_save_word_rule():
    """
    读取 resources/word/CET6luan_1trans_len_8_10.json 中的单词，在 SRT 字幕文件中查找指定中文词语
    """
    print("🚀 开始处理词汇文件...")
    
    # 读取词汇文件
    try:
        with open("resources/word/CET6luan_1trans_len_8_10.json", "r", encoding='utf-8') as f:
            word_data = json.load(f)
    except Exception as e:
        print(f"❌ 读取词汇文件失败: {e}")
        return
    
    print(f"📚 共加载 {len(word_data)} 个词汇")
    
    # 创建输出目录
    output_dir = "resources/word_search"
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理每个词汇
    for idx, word_item in enumerate(word_data, 1):
        try:
            # 提取词汇信息
            english_word = word_item.get('word', '')
            us_phonetic = word_item.get('us', '')
            uk_phonetic = word_item.get('uk', '')
            translations_list = word_item.get('translations', [])
            
            if not translations_list:
                print(f"⚠️  [{idx}/{len(word_data)}] {english_word}: 无翻译信息，跳过")
                continue
            
            # 获取第一个翻译
            main_translation = translations_list[0]
            chinese_word = main_translation.get('translation', '')
            word_type = main_translation.get('type', '')
            
            if not chinese_word:
                print(f"⚠️  [{idx}/{len(word_data)}] {english_word}: 翻译为空，跳过")
                continue
            
            print(f"🔍 [{idx}/{len(word_data)}] 正在检索: {english_word} -> {chinese_word}")
            
            # 使用现有的批量检索功能
            results = search_word_in_multiple_srt("resources/srt/zh", chinese_word)
            
            if not results:
                print(f"   - 未找到匹配项")
                continue
            
            # 整合结果，转换为目标格式
            consolidated_result = {
                "word": english_word,
                "word_type": word_type,
                "us_phonetic": f"/{us_phonetic}/" if us_phonetic else "",
                "uk_phonetic": f"/{uk_phonetic}/" if uk_phonetic else "",
                "translations": [f"{word_type}. {chinese_word}"] if word_type else [chinese_word],
                "total_occurrences": 0,
                "occurrences": []
            }
            
            # 合并所有SRT文件的结果
            total_occurrences = 0
            for result in results:
                srt_name = result["srt_name"]
                # 提取文件名中的集数（如：S01E01）
                srt_file_code = os.path.splitext(srt_name)[0].replace('.zho', '')
                
                for target in result["target"]:
                    occurrence = {
                        "timestamp": target["timestamp"],
                        "index": target["index"],
                        "srt_file": srt_file_code,
                        "text": target["subtitle"],
                        "context_before": target["context_before"],
                        "context_after": target["context_after"]
                    }
                    consolidated_result["occurrences"].append(occurrence)
                    total_occurrences += 1
            
            consolidated_result["total_occurrences"] = total_occurrences
            
            # 生成输出文件名
            safe_word = english_word.replace('/', '_').replace('\\', '_')
            output_filename = f"step1_{idx}_{word_type}_{safe_word}.json"
            output_path = os.path.join(output_dir, output_filename)
            
            # 保存结果（注意：输出格式是包含单个词汇信息的数组）
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump([consolidated_result], f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 找到 {total_occurrences} 个匹配项，已保存到: {output_filename}")
            
        except Exception as e:
            print(f"❌ [{idx}/{len(word_data)}] 处理词汇 {word_item.get('word', '未知')} 时出错: {e}")
            continue
    
    print(f"\n🎉 词汇批量检索完成！")
    print(f"📁 结果文件保存在: {output_dir}")


def batch_search_and_save_word_rule_single(word_index: int = None, start_index: int = 1, end_index: int = None):
    """
    批量检索单个或指定范围的词汇（用于测试或分批处理）
    
    Args:
        word_index: 处理指定索引的词汇（从1开始）
        start_index: 开始索引（从1开始）
        end_index: 结束索引（包含）
    """
    print("🚀 开始处理词汇文件...")
    
    # 读取词汇文件
    try:
        with open("resources/word/CET6luan_1trans_len_8_10.json", "r", encoding='utf-8') as f:
            word_data = json.load(f)
    except Exception as e:
        print(f"❌ 读取词汇文件失败: {e}")
        return
    
    print(f"📚 共加载 {len(word_data)} 个词汇")
    
    # 确定处理范围
    if word_index is not None:
        start_index = word_index
        end_index = word_index
    elif end_index is None:
        end_index = len(word_data)
    
    # 调整索引（转换为0基索引）
    start_idx = max(0, start_index - 1)
    end_idx = min(len(word_data), end_index)
    
    print(f"🎯 处理范围: {start_index} 到 {end_index} ({end_idx - start_idx} 个词汇)")
    
    # 创建输出目录
    output_dir = "resources/word_search_rule_v2/"
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理指定范围的词汇
    for idx in range(start_idx, end_idx):
        word_item = word_data[idx]
        display_idx = idx + 1
        
        try:
            # 提取词汇信息
            english_word = word_item.get('word', '')
            us_phonetic = word_item.get('us', '')
            uk_phonetic = word_item.get('uk', '')
            translations_list = word_item.get('translations', [])
            
            if not translations_list:
                print(f"⚠️  [{display_idx}/{len(word_data)}] {english_word}: 无翻译信息，跳过")
                continue
            
            # 获取第一个翻译
            main_translation = translations_list[0]
            chinese_word = main_translation.get('translation', '').split('，')[0].split('；')[0]
            word_type = main_translation.get('type', '')
            
            if not chinese_word:
                print(f"⚠️  [{display_idx}/{len(word_data)}] {english_word}: 翻译为空，跳过")
                continue
            
            print(f"🔍 [{display_idx}/{len(word_data)}] 正在检索: {english_word} -> {chinese_word}")
            
            # 使用现有的批量检索功能
            results = search_word_in_multiple_srt("resources/srt/zh", chinese_word)
            
            if not results:
                print(f"   - 未找到匹配项")
                continue
            
            # 整合结果，转换为目标格式
            consolidated_result = {
                "word": english_word,
                "word_type": word_type,
                "us_phonetic": f"/{us_phonetic}/" if us_phonetic else "",
                "uk_phonetic": f"/{uk_phonetic}/" if uk_phonetic else "",
                "translations": [f"{word_type}. {chinese_word}"] if word_type else [chinese_word],
                "total_occurrences": 0,
                "occurrences": []
            }
            
            # 合并所有SRT文件的结果
            total_occurrences = 0
            for result in results:
                srt_name = result["srt_name"]
                # 提取文件名中的集数（如：S01E01）
                srt_file_code = os.path.splitext(srt_name)[0].replace('.zho', '')
                
                for target in result["target"]:
                    occurrence = {
                        "timestamp": target["timestamp"],
                        "index": target["index"],
                        "srt_file": srt_file_code,
                        "text": target["subtitle"],
                        "context_before": target["context_before"],
                        "context_after": target["context_after"]
                    }
                    consolidated_result["occurrences"].append(occurrence)
                    total_occurrences += 1
            
            consolidated_result["total_occurrences"] = total_occurrences
            
            # 生成输出文件名
            safe_word = english_word.replace('/', '_').replace('\\', '_')
            output_filename = f"step1_{display_idx}_{word_type}_{safe_word}.json"
            output_path = os.path.join(output_dir, output_filename)
            
            # 保存结果（注意：输出格式是包含单个词汇信息的数组）
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump([consolidated_result], f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 找到 {total_occurrences} 个匹配项，已保存到: {output_filename}")
            
        except Exception as e:
            print(f"❌ [{display_idx}/{len(word_data)}] 处理词汇 {word_item.get('word', '未知')} 时出错: {e}")
            continue
    
    print(f"\n🎉 词汇批量检索完成！")
    print(f"📁 结果文件保存在: {output_dir}")


# 主函数（不包含测试代码）
if __name__ == "__main__":
    # 用户可以手动调用批量检索功能
    # 示例调用方式：
    batch_search_and_save_word_rule_single()
