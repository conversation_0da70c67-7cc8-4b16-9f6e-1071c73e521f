#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project: tv_learning
@File   : 最后一步导出成品视频
<AUTHOR> 小林同学
@Date   : 2025/5/15 上午12:08
'''
from moviepy import VideoFileClip, TextClip, CompositeVideoClip, ColorClip
import os
import platform
from loguru import logger


def merge_video_materials(video_path, text_word='',
                          text_us_phonetic='', translations='', output_path='output.mp4',
                          vertical_mode=False, vertical_width=1080):
    """
    合并视频和文本为一个竖屏视频

    参数:
        video_path (str): 视频文件路径
        text_word (str): 要显示的单词
        text_us_phonetic (str): 音标
        translations (str): 翻译
        output_path (str): 输出视频路径
        vertical_mode (bool): 是否为竖屏模式
        vertical_width (int): 竖屏宽度

    返回:
        str: 输出视频的路径
    """
    # 获取项目根路径
    script_dir = os.path.join(os.getcwd())

    # 选择字体路径 - 为不同的文本类型使用不同的字体
    # 中文字体
    # chinese_font_path = os.path.join(script_dir, 'fonts/Source_Han_Sans_SC_Bold.otf')
    chinese_font_path = '/Users/<USER>/Desktop/home/<USER>/fonts/Source_Han_Sans_SC_Bold.otf'
    logger.debug("字体路径：{}".format(chinese_font_path))
    # 查找系统上可能存在的音标字体
    system_name = platform.system()
    if system_name == "Darwin":  # macOS
        # macOS上常见的音标字体
        phonetic_fonts = [
            "/Library/Fonts/Arial Unicode.ttf",  # 尝试Arial Unicode MS
            "/System/Library/Fonts/Supplemental/Arial.ttf",  # 尝试Arial
            "/Library/Fonts/Times New Roman.ttf",  # 尝试Times New Roman
            chinese_font_path  # 如果系统字体都不存在，回退到中文字体
        ]
    elif system_name == "Windows":
        # Windows上常见的音标字体
        phonetic_fonts = [
            "C:\\Windows\\Fonts\\arialuni.ttf",  # Arial Unicode MS
            "C:\\Windows\\Fonts\\arial.ttf",  # Arial
            "C:\\Windows\\Fonts\\times.ttf",  # Times New Roman
            chinese_font_path  # 回退选项
        ]
    else:  # Linux或其他系统
        phonetic_fonts = [
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # DejaVu Sans
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",  # Liberation Sans
            chinese_font_path  # 回退选项
        ]

    # 查找第一个存在的音标字体
    phonetic_font_path = None
    for font_path in phonetic_fonts:
        if os.path.exists(font_path):
            phonetic_font_path = font_path
            break

    # 如果没有找到任何音标字体，使用默认字体
    if phonetic_font_path is None:
        phonetic_font_path = chinese_font_path

    # 1. 加载视频
    video_clip = VideoFileClip(video_path)

    # 获取原始视频尺寸
    original_width, original_height = video_clip.size

    # 2. 处理竖屏模式
    if vertical_mode:
        # 固定竖屏尺寸为1080x1920 (9:16比例)
        vertical_width = 1080
        vertical_height = 1920

        # 计算视频在竖屏中的高度，保持原始比例
        new_height = int(original_height * (vertical_width / original_width))

        # 调整视频大小
        video_clip = video_clip.resized(width=vertical_width)

        # 创建黑色背景
        background = ColorClip(size=(vertical_width, vertical_height), color=(0, 0, 0))
        background = background.with_duration(video_clip.duration)

        # 计算文本所需的总高度
        word_height = 70  # 单词字体大小(60)加间距(10)
        phonetic_height = 50  # 音标字体大小(40)加间距(10)
        translation_height = 50  # 翻译字体大小(40)加间距(10)
        text_spacing = 50  # 文本与视频之间的间距

        # 只计算实际会使用的文本高度
        text_elements_height = 0
        if text_word:
            text_elements_height += word_height
        if text_us_phonetic:
            text_elements_height += phonetic_height
        if translations:
            text_elements_height += translation_height

        # 加上文本与视频之间的间距
        if text_elements_height > 0:
            text_elements_height += text_spacing

        # 视频在整个竖屏中的垂直居中位置
        # 这里我们需要考虑到文本要放在下方，所以视频应该在(竖屏高度 - 文本高度)/2的位置上居中
        center_area_height = vertical_height - text_elements_height
        video_y_position = int((center_area_height - new_height) / 2)

        # 计算视频在背景中的位置，水平居中，垂直居中
        video_pos = ("center", video_y_position)
        video_clip = video_clip.with_position(video_pos)

        # 创建一个画布
        canvas = CompositeVideoClip([background, video_clip], size=(vertical_width, vertical_height))

        # 文本将在视频下方显示，留出足够间距
        text_start_y = video_y_position + new_height + text_spacing
    else:
        # 横屏模式，文本将显示在视频底部
        canvas = video_clip.copy()
        text_start_y = original_height - 150  # 文本在底部，留出空间

    # 3. 创建文本元素
    clips = [canvas]

    # 当前文本位置
    y_position = text_start_y

    # 创建单词文本
    if text_word:
        word_clip = TextClip(text=text_word, font_size=60, color='white', font=chinese_font_path)
        word_clip = word_clip.with_position(("center", y_position)).with_duration(video_clip.duration)
        clips.append(word_clip)
        y_position += 70

    # 创建音标文本 - 使用专门的音标字体
    if text_us_phonetic:
        # 确保音标使用支持IPA的字体
        try:
            phonetic_clip = TextClip(text=text_us_phonetic, font_size=40, color='white', font=phonetic_font_path)
            phonetic_clip = phonetic_clip.with_position(("center", y_position)).with_duration(video_clip.duration)
            clips.append(phonetic_clip)
        except Exception as e:
            print(f"音标显示错误: {e}，尝试使用备用方法")
            # 如果出错，尝试使用Pillow默认字体(该字体通常支持音标)
            phonetic_clip = TextClip(text=text_us_phonetic, font_size=40, color='white')
            phonetic_clip = phonetic_clip.with_position(("center", y_position)).with_duration(video_clip.duration)
            clips.append(phonetic_clip)
        y_position += 50

    # 创建翻译文本
    if translations:
        trans_clip = TextClip(text=translations, font_size=40, color='white', font=chinese_font_path)
        trans_clip = trans_clip.with_position(("center", y_position)).with_duration(video_clip.duration)
        clips.append(trans_clip)

    # 4. 合成最终视频
    final_clip = CompositeVideoClip(clips)

    # 5. 输出最终视频
    final_clip.write_videofile(output_path, codec='libx264', audio_codec='aac')

    # 关闭所有clip以释放资源
    final_clip.close()
    video_clip.close()

    return output_path


if __name__ == '__main__':
    # 使用示例
    video_path = '/Users/<USER>/Desktop/home/<USER>/work/2025-05-24-25/step7_final_video/final_merged_video.mp4'
    text_word = 'retribution'
    text_us_phonetic = '/ rɛtrɪˈbjuːʃ(ə)n /'
    translations = 'n. 报应，惩罚，报答'

    output = merge_video_materials(
        video_path=video_path,
        text_word=text_word,
        text_us_phonetic=text_us_phonetic,
        translations=translations,
        output_path='final_output.mp4',
        vertical_mode=True,  # 启用竖屏模式
        vertical_width=1080  # 竖屏宽度为1080像素
    )
