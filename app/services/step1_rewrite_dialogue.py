#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project: tv_learning
@File   : 台词重写 - 根据上下文提取出单个说话人的片段, 并进行重写或者合并
<AUTHOR> 小林同学
@Date   : 2025/5/13 上午11:53 
'''
import json
import time
from gemini import chat
from app.utils.youdao import get_word_meaning


def assemble_subtitles(step1_data: dict):
    """
    提取单词和原字幕数据，组装成用于重写的格式
    
    参数:
        step1_data: 包含单词及其在各个视频中出现位置的数据
        
    返回:
        result: 包含以下字段的列表
            - word: 目标单词
            - translations: 单词翻译
            - video: 视频文件名
            - subtitles: 原始字幕列表(含时间码，用于后期合并到视频)
            - text: 拼接后的完整字幕文本(用于AI重写)
    """
    result = []
    occurrences = step1_data['occurrences']

    for occurrence in occurrences:
        occ = {
            "word": step1_data['word'],
            "translations": step1_data['translations'],
            "video": occurrence['srt_file'],
            "subtitles": [],  # 保存原始字幕格式，包含时间码
            "text": [],       # 拼接后的文本，用于AI处理
        }
        # 中文前 3 段
        before_list = occurrence['context_before'][0:3]
        for line in before_list:
            timestamp = line['timestamp']
            subtitle = line['text']
            occ['subtitles'].append(f"{timestamp}\n{subtitle}\n")
        # 中文 中间核心段
        timestamp = occurrence['timestamp']
        text_zh = occurrence['text']
        occ['subtitles'].append(f"{timestamp}\n{text_zh}\n")
        # 中文 后 3 段
        after_list = occurrence['context_after'][0:3]
        for line in after_list:
            timestamp = line['timestamp']
            subtitle = line['text']
            occ['subtitles'].append(f"{timestamp}\n{subtitle}\n")
        
        # 将所有字幕拼接成完整文本，供AI重写使用
        occ['text'] = "\n\n".join(occ['subtitles'])

        result.append(occ)
    return result


def rewrite_dialogue(apikey, model, temperature, video_name, srt_line, word, target_srt, max_retries=5, retry_delay=3):
    """
    重写字幕，包含重试机制
    
    参数:
        apikey: API密钥
        model: 使用的模型名称
        temperature: 温度参数
        video_name: 视频名称
        srt_line
        word
        target_srt
        max_retries: 最大重试次数，默认3次
        retry_delay: 重试间隔时间(秒)，默认3秒
    """
    messages = [
        {"role": "system", "content": """
**角色设定：**
你是一名资深的字幕处理工程师和语言学专家。你的核心任务是精确地识别和合并字幕文件中属于同一完整句子的条目，同时严格保留原始文本内容和计算新的时间戳。
"""
         },
        {"role": "user", "content": """
**任务目标：**
分析提供的SRT字幕片段，重点识别语法上、逻辑上和语义上构成一个完整连贯句子的所有连续字幕条目。一旦确定，你需要将这些条目合并为一个新的SRT条目，包括新的起止时间戳和拼接后的完整文本内容。

**输入数据：**

```srt
%s
```

请特别关注包含指定词语“%s”的字幕条目 `%s` 及其前后的上下文。

**分析与合并规则：**

1.  **语法结构分析：**
    *   识别是否存在关联词（如“无论是...还是...”、“只要...就要...”）或其他句式连接词，这些词通常指示一个句子的延续。
    *   分析各字幕条目内部及相互之间的主谓宾结构、修饰关系，判断它们是否共同构成一个完整的句法单位。
    *   注意隐含的句中停顿和语气连贯性，而非仅依赖字幕中的显式标点（字幕中常省略或简化标点）。

2.  **逻辑关系推断：**
    *   评估字幕条目之间的因果、条件、并列、转折等逻辑关系。如果多个条目共同表达一个完整的逻辑思想，则很可能属于同一句话。

3.  **语义连贯性判断：**
    *   将相邻的字幕文本在脑海中连读，判断其是否形成一个意义完整、表达流畅的句子。如果拆分会造成语义上的断裂或不完整，则应合并。

4.  **时间戳合并：**
    *   如果判断多个字幕条目构成同一句话，则新的合并条目的开始时间戳应取这组条目中**第一个条目的开始时间戳**。
    *   新的合并条目的结束时间戳应取这组条目中**最后一个条目的结束时间戳**。

5.  **文本内容合并：**
    *   将所有构成同一句话的字幕条目的文本内容**直接拼接**起来，中间使用空额间隔。

**输出格式 (Output Format):** 严格使用 json 格式输出合并后的字幕行，如：
{
     "timestamp": "00:14:10,870 --> 00:14:12,720",
     "origin": "户籍登记地址是勃北市宁远乡莞香村",
}

最后是结果应该只有一个字幕行；
除了要求输出的 json 数据，不要输出其他任何内容；
""" % (srt_line, word, target_srt)
         },
    ]

    for retry in range(max_retries):
        try:
            result = chat(apikey, model, messages, temperature)
            result = result['content'].replace("```json", "").replace("```", "")
            res = json.loads(result)
            res['video'] = video_name
            return res
        except Exception as e:
            if retry < max_retries - 1:
                print(f"请求失败，错误: {e}，{retry + 1}/{max_retries} 次重试，等待 {retry_delay} 秒...")
                time.sleep(retry_delay)
                return None
            else:
                print(f"重试 {max_retries} 次后仍然失败: {e}")
                raise  # 重新抛出异常，允许调用者处理最终失败
    return None


if __name__ == '__main__':
    apikey = "sk-narra-ZPA07ethehTb2VQ5mMI1P8tt"
    model = "gemini-2.0-flash"
    temperature = 0.7

    # 读取单词数据
    with open("/Users/<USER>/Desktop/home/<USER>/resources/word_search_rule_v2/step1_57_n_property.json", "r") as f:
        word_data = json.load(f)
    
    # 组装字幕数据
    srt = assemble_subtitles(word_data[0])
    print(f"共找到 {len(srt)} 个字幕片段需要处理")

    rewrites = []
    processed_count = 0
    
    for i in srt:
        word = i['word']
        srt_line = i['text']
        video_name = i['video']
        # 保存原始字幕信息，供后期合并到视频时使用
        original_subtitles = i['subtitles']
        
        processed_count += 1
        print(f"正在处理第 {processed_count}/{len(srt)} 个片段，单词: {word}, 视频: {video_name}")

        try:
            res = rewrite_dialogue(
                apikey=apikey,
                model=model,
                temperature=temperature,
                video_name=video_name,
                srt_line=srt_line,
                word=word
            )
            if word in res['text']:
                # 新增 subtitle 字段，记录原始字幕信息，方便后期合并到视频中
                res['subtitle'] = original_subtitles
                print(f"✓ 重写成功: {res['text']}")
                rewrites.append(res)
            else:
                print(f"✗ 重写结果中未包含目标单词 '{word}'，跳过该片段")
        except Exception as e:
            print(f"✗ 处理字幕失败: {e}")
            continue

    print(f"\n处理完成！成功重写 {len(rewrites)} 个字幕片段")
    
    # 将提取出的元素写入新的 JSON 文件
    with open("rewrite_v2.json", "w") as f:
        json.dump(rewrites, f, ensure_ascii=False, indent=4)
    
    print(f"结果已保存到 rewrite_v2.json 文件中")
