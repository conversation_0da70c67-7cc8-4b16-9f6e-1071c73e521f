#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project: tv_learning
@File   : 合并单个片段的 视频、音频、字幕
<AUTHOR> 小林同学
@Date   : 2025/5/16 上午10:00
'''

import os
import json
import shutil
import subprocess
import platform
from loguru import logger
from typing import List, Dict, Optional, Tuple, Any


def get_hardware_acceleration_params() -> List[str]:
    """
    根据当前操作系统获取适合的硬件加速参数

    返回:
        List[str]: 硬件加速参数列表
    """
    system = platform.system()
    if system == "Darwin":  # macOS
        return ["-hwaccel", "videotoolbox"]
    elif system == "Windows":
        return ["-hwaccel", "dxva2"]
    elif system == "Linux":
        return ["-hwaccel", "vaapi", "-vaapi_device", "/dev/dri/renderD128"]
    return []


def ensure_directory_exists(directory_path: str) -> None:
    """
    确保目录存在，如不存在则创建

    参数:
        directory_path: 目录路径
    """
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)


def milliseconds_to_ass_time(ms: float) -> str:
    """
    将毫秒转换为ASS时间格式 (H:MM:SS.CC)

    参数:
        ms: 毫秒数

    返回:
        str: ASS格式时间字符串
    """
    total_seconds = ms / 1000.0
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = total_seconds % 60
    return f"{hours}:{minutes:02d}:{seconds:05.2f}"


def find_target_word_timing(asr_words: List[Dict], target_word: str) -> Optional[Tuple[float, float]]:
    """
    在ASR词汇列表中找到目标词汇的时间范围

    参数:
        asr_words: ASR词汇列表
        target_word: 目标词汇

    返回:
        Optional[Tuple[float, float]]: (开始时间ms, 结束时间ms)，未找到返回None
    """
    target_chars = list(target_word)
    if not target_chars:
        return None

    # 在ASR词汇中查找目标词汇的字符序列
    for i in range(len(asr_words) - len(target_chars) + 1):
        # 检查从位置i开始是否匹配目标词汇
        match = True
        for j, char in enumerate(target_chars):
            if i + j >= len(asr_words) or asr_words[i + j]['text'] != char:
                match = False
                break

        if match:
            # 找到匹配，计算时间范围
            start_time = asr_words[i]['offset_ms']
            end_time = asr_words[i + len(target_chars) - 1]['offset_ms'] + asr_words[i + len(target_chars) - 1]['duration_ms']
            return start_time, end_time

    return None


def create_advanced_subtitle_file(video_data: Dict[str, Any], output_path: str) -> str:
    """
    基于ASR词汇数据创建高级ASS字幕文件，支持目标词汇高亮和英文显示

    参数:
        video_data: 包含ASR数据和目标词汇的视频数据
        output_path: 输出ASS字幕文件路径

    返回:
        str: 字幕文件路径
    """
    # 确保输出目录存在
    ensure_directory_exists(os.path.dirname(output_path))

    # 获取数据
    asr_words = video_data.get('asr_words', {}).get('words', [])
    # 优先使用origin字段（原始中文），如果没有则使用text字段
    full_text = video_data.get('origin', video_data.get('text', ''))
    target_zh = video_data.get('word_zh', '')
    target_en = video_data.get('word_en', '')

    if not asr_words:
        logger.warning("ASR词汇数据为空，使用简单字幕")
        return create_simple_subtitle_file(full_text, output_path)

    # 查找目标词汇的时间范围
    target_timing = find_target_word_timing(asr_words, target_zh) if target_zh else None

    # 创建ASS文件内容
    ass_content = create_ass_header()

    # 计算总时长
    if asr_words:
        total_duration = asr_words[-1]['offset_ms'] + asr_words[-1]['duration_ms']
    else:
        total_duration = 5000  # 默认5秒

    start_time = milliseconds_to_ass_time(0)
    end_time = milliseconds_to_ass_time(total_duration)

    if target_timing and target_zh and target_en and target_zh in full_text:
        # 有目标词汇的情况，创建分段字幕
        target_start, target_end = target_timing

        target_start_time = milliseconds_to_ass_time(target_start)
        target_end_time = milliseconds_to_ass_time(target_end)

        # 1. 目标词汇出现前的普通字幕
        if target_start > 0:
            ass_content += f"Dialogue: 0,{start_time},{target_start_time},Default,,0,0,0,,{full_text}\\N\n"

        # 2. 目标词汇时间段开始：高亮中文 + 英文显示
        # 创建高亮的中文字幕（高亮后持续到结束）
        highlighted_text = full_text.replace(target_zh, f"{{\\c&H00FFFF&}}{target_zh}{{\\c&HFFFFFF&}}")

        # 创建英文替换的字幕行（用于精确定位英文单词）
        # 将中文目标词汇替换为英文，其他文字设为透明
        english_positioned_text = full_text.replace(target_zh, f"{{\\c&H00FFFF&\\b1}}{target_en}{{\\c&H00000000&}}")
        # 将替换后文本中除了英文单词外的所有字符设为透明
        # 先找到目标词汇的位置
        target_pos = full_text.find(target_zh)
        if target_pos >= 0:
            # 构建透明字幕：目标词汇前的文字 + 高亮英文 + 目标词汇后的文字
            before_text = full_text[:target_pos]
            after_text = full_text[target_pos + len(target_zh):]

            # 创建透明的定位字幕行，添加向上偏移
            # 使用 \pos 标签来精确控制位置，向上移动约40像素（一个字幕行的高度）
            transparent_before = f"{{\\c&H00000000&\\alpha&HFF&}}{before_text}" if before_text else ""
            highlighted_english = f"{{\\c&H00FFFF&\\b1\\alpha&H00&\\pos(640,320)}}{target_en}"  # 向上偏移
            transparent_after = f"{{\\c&H00000000&\\alpha&HFF&}}{after_text}" if after_text else ""

            # 为了保持水平对齐，我们需要使用不同的方法
            # 使用MarginV来向上偏移整行字幕
            english_positioned_text = transparent_before + f"{{\\c&H00FFFF&\\b1\\alpha&H00&}}{target_en}" + transparent_after
        else:
            # 如果找不到目标词汇位置，使用居中显示并向上偏移
            english_positioned_text = f"{{\\an2\\c&H00FFFF&\\b1}}{target_en}"

        # 添加字幕行
        # Layer 0: 中文字幕（目标词汇高亮，从目标词汇开始时间持续到结束）
        ass_content += f"Dialogue: 0,{target_start_time},{end_time},Default,,0,0,0,,{highlighted_text}\\N\n"

        # Layer 1: 透明的英文定位字幕（从目标词汇开始时间持续到字幕结束，向上偏移60像素）
        ass_content += f"Dialogue: 1,{target_start_time},{end_time},Default,,0,0,60,,{english_positioned_text}\\N\n"

    else:
        # 没有目标词汇或数据不完整，使用普通字幕
        ass_content += f"Dialogue: 0,{start_time},{end_time},Default,,0,0,0,,{full_text}\\N\n"
        if target_zh and target_zh not in full_text:
            logger.warning(f"目标词汇 '{target_zh}' 在文本 '{full_text}' 中未找到")

    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(ass_content)

    logger.debug(f"创建高级字幕文件: {output_path}")
    logger.debug(f"  - 使用文本: {full_text}")
    if target_timing:
        logger.debug(f"  - 目标词汇 '{target_zh}' 时间: {target_timing[0]:.0f}ms - {target_timing[1]:.0f}ms")
        logger.debug(f"  - 高亮效果持续到字幕结束")

    return output_path


def create_ass_header() -> str:
    """
    创建ASS字幕文件的头部信息

    返回:
        str: ASS头部内容
    """
    return """[Script Info]
Title: TV Learning Subtitle
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: TV.601

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,28,&Hffffff,&Hffffff,&H0,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,30,1
Style: English,Arial,30,&H00ffff,&H00ffff,&H0,&H80000000,1,0,0,0,100,100,0,0,1,2,0,8,10,10,65,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""


def create_simple_subtitle_file(text: str, output_path: str) -> str:
    """
    创建简单的ASS字幕文件（当ASR数据不可用时的备用方案）

    参数:
        text: 字幕文本
        output_path: 输出文件路径

    返回:
        str: 字幕文件路径
    """
    ensure_directory_exists(os.path.dirname(output_path))

    ass_content = create_ass_header()
    ass_content += f"Dialogue: 0,0:00:00.00,0:00:05.00,Default,,0,0,0,,{text}\\N\n"

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(ass_content)

    return output_path


def create_subtitle_file(text: str, timestamp: str, output_path: str) -> str:
    """
    根据文本和时间戳创建SRT字幕文件

    参数:
        text: 字幕文本
        timestamp: 时间戳范围 (格式: '00:00:00,000 --> 00:00:00,000')
        output_path: 输出字幕文件路径

    返回:
        str: 字幕文件路径
    """
    # 确保输出目录存在
    ensure_directory_exists(os.path.dirname(output_path))

    # 将原始时间戳转换为从0开始的时间戳
    adjusted_timestamp = convert_to_zero_based_timestamp(timestamp)

    # 写入SRT格式字幕
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("1\n")
        f.write(f"{adjusted_timestamp}\n")
        f.write(f"{text}\n")

    return output_path


def convert_to_zero_based_timestamp(timestamp: str) -> str:
    """
    将原始时间戳转换为从0开始的时间戳

    参数:
        timestamp: 原始时间戳 (格式: '00:00:00,000 --> 00:00:00,000')

    返回:
        str: 从0开始的时间戳
    """
    # 拆分时间戳
    start_timestamp, end_timestamp = timestamp.split(' --> ')

    # 将时间戳转换为秒
    def timestamp_to_seconds(ts):
        hours, minutes, rest = ts.split(':')
        seconds, milliseconds = rest.split(',')
        total_seconds = int(hours) * 3600 + int(minutes) * 60 + int(seconds) + int(milliseconds) / 1000
        return total_seconds

    # 获取开始和结束的秒数
    start_seconds = timestamp_to_seconds(start_timestamp)
    end_seconds = timestamp_to_seconds(end_timestamp)

    # 计算持续时间
    duration = end_seconds - start_seconds

    # 从0开始的新时间戳
    new_start_timestamp = "00:00:00,000"

    # 构建结束时间戳
    milliseconds = int(duration * 1000) % 1000
    seconds = int(duration) % 60
    minutes = int(duration / 60) % 60
    hours = int(duration / 3600)

    new_end_timestamp = f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

    return f"{new_start_timestamp} --> {new_end_timestamp}"


def get_media_duration(file_path: str) -> float:
    """
    获取媒体文件的时长（秒）

    参数:
        file_path: 媒体文件路径

    返回:
        float: 时长（秒），失败时返回0
    """
    try:
        command = [
            "ffprobe",
            "-v", "quiet",
            "-show_entries", "format=duration",
            "-of", "csv=p=0",
            file_path
        ]
        result = subprocess.run(command, capture_output=True, text=True, check=True)
        return float(result.stdout.strip())
    except (subprocess.CalledProcessError, ValueError) as e:
        logger.warning(f"获取媒体时长失败: {file_path}, 错误: {e}")
        return 0.0


def merge_video_audio(video_path: str, audio_path: str, output_path: str,
                     use_hw_accel: bool = True, fade_duration: float = 1.0) -> bool:
    """
    合并视频和音频，确保音频完整播放。如果视频比音频短，会延长视频并添加淡化效果

    参数:
        video_path: 视频文件路径
        audio_path: 音频文件路径
        output_path: 输出文件路径
        use_hw_accel: 是否使用硬件加速
        fade_duration: 淡化效果持续时间（秒）

    返回:
        bool: 合并是否成功
    """
    # 确保输出目录存在
    ensure_directory_exists(os.path.dirname(output_path))

    # 获取视频和音频的时长
    video_duration = get_media_duration(video_path)
    audio_duration = get_media_duration(audio_path)

    if video_duration == 0 or audio_duration == 0:
        logger.error(f"无法获取媒体时长: 视频={video_duration}s, 音频={audio_duration}s")
        return False

    logger.debug(f"媒体时长 - 视频: {video_duration:.2f}s, 音频: {audio_duration:.2f}s")

    # 获取硬件加速参数
    hw_accel_params = get_hardware_acceleration_params() if use_hw_accel else []

    if video_duration >= audio_duration:
        # 视频比音频长或相等，直接合并，以音频长度为准
        logger.debug("视频时长足够，直接合并")
        command = [
            "ffmpeg",
            "-y",  # 覆盖已存在文件
            *hw_accel_params,
            "-i", video_path,  # 视频输入
            "-i", audio_path,  # 音频输入
            "-c:v", "copy",  # 复制视频流，不重新编码
            "-c:a", "aac",  # 音频编码为AAC
            "-map", "0:v",  # 使用第一个输入（视频）的视频流
            "-map", "1:a",  # 使用第二个输入（音频）的音频流
            "-t", str(audio_duration),  # 以音频时长为准
            output_path
        ]
    else:
        # 视频比音频短，需要延长视频并添加淡化效果
        logger.debug(f"视频时长不足，需要延长 {audio_duration - video_duration:.2f}s")

        # 计算淡化开始时间（从视频结束前fade_duration秒开始淡化）
        fade_start = max(0, video_duration - fade_duration)

        # 创建视频滤镜：循环最后一帧并添加淡化效果
        video_filter = (
            f"[0:v]split=2[v1][v2];"
            f"[v1]trim=0:{video_duration}[main];"
            f"[v2]trim={video_duration - 0.05}:{video_duration},loop=loop=-1:size=1:start=0,"
            f"fade=t=in:st=0:d={fade_duration}:alpha=1,"
            f"fade=t=out:st={fade_duration}:d={min(fade_duration, audio_duration - video_duration)}:alpha=1"
            f"[extended];"
            f"[main][extended]concat=n=2:v=1:a=0,trim=0:{audio_duration}[vout]"
        )

        command = [
            "ffmpeg",
            "-y",  # 覆盖已存在文件
            *hw_accel_params,
            "-i", video_path,  # 视频输入
            "-i", audio_path,  # 音频输入
            "-filter_complex", video_filter,
            "-map", "[vout]",  # 使用处理后的视频流
            "-map", "1:a",  # 使用音频流
            "-c:a", "aac",  # 音频编码为AAC
            "-t", str(audio_duration),  # 确保输出时长等于音频时长
            output_path
        ]

    try:
        result = subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        logger.debug(f"成功合并音视频: {output_path}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"合并音视频失败: {e}")
        logger.error(f"错误输出: {e.stderr}")

        # 如果复杂滤镜失败，尝试简单的方案
        if video_duration < audio_duration:
            logger.debug("尝试使用简单的视频延长方案")
            return _merge_video_audio_simple_extend(video_path, audio_path, output_path,
                                                   audio_duration, use_hw_accel)
        return False


def _merge_video_audio_simple_extend(video_path: str, audio_path: str, output_path: str,
                                    audio_duration: float, use_hw_accel: bool = True) -> bool:
    """
    简单的视频延长方案：重复最后一帧

    参数:
        video_path: 视频文件路径
        audio_path: 音频文件路径
        output_path: 输出文件路径
        audio_duration: 音频时长
        use_hw_accel: 是否使用硬件加速

    返回:
        bool: 合并是否成功
    """
    try:
        # 获取硬件加速参数
        hw_accel_params = get_hardware_acceleration_params() if use_hw_accel else []

        # 使用tpad滤镜延长视频，重复最后一帧
        extend_duration = audio_duration

        command = [
            "ffmpeg",
            "-y",  # 覆盖已存在文件
            *hw_accel_params,
            "-i", video_path,  # 视频输入
            "-i", audio_path,  # 音频输入
            "-filter_complex", f"[0:v]tpad=stop_mode=clone:stop_duration={extend_duration}[v]",
            "-map", "[v]",  # 使用延长后的视频流
            "-map", "1:a",  # 使用音频流
            "-c:a", "aac",  # 音频编码为AAC
            "-t", str(audio_duration),  # 确保输出时长等于音频时长
            output_path
        ]

        subprocess.run(command, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        logger.debug(f"使用简单方案成功合并音视频: {output_path}")
        return True

    except subprocess.CalledProcessError as e:
        logger.error(f"简单方案合并音视频也失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False


def add_subtitles_to_video(video_path: str, subtitle_path: str, output_path: Optional[str] = None,
                          font_size: int = 28, font_color: str = 'FFFFFF', border_color: str = '000000',
                          border_width: float = 2.0, position: str = 'bottom',
                          top_margin: int = 10, bottom_margin: int = 30) -> Optional[str]:
    """
    为视频添加字幕，优化支持ASS格式

    参数:
        video_path: 视频文件路径
        subtitle_path: 字幕文件路径（srt, ass等格式）
        output_path: 输出文件路径，默认在原视频路径后添加"_with_subtitles"
        font_size: 字幕字体大小
        font_color: 字幕颜色（十六进制，不带#）
        border_color: 字幕边框颜色（十六进制，不带#）
        border_width: 字幕边框宽度
        position: 字幕位置，可选 'bottom', 'top', 'middle'
        top_margin: 当position为'top'时，距离顶部的像素距离
        bottom_margin: 当position为'bottom'时，距离底部的像素距离

    返回:
        Optional[str]: 成功时返回输出视频路径，失败时返回None
    """
    if not os.path.exists(video_path):
        logger.error(f"视频文件不存在: {video_path}")
        return None

    if not os.path.exists(subtitle_path):
        logger.error(f"字幕文件不存在: {subtitle_path}")
        return None

    # 如果没有指定输出路径，创建默认输出路径
    if output_path is None:
        video_dir = os.path.dirname(video_path)
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        video_ext = os.path.splitext(video_path)[1]
        output_path = os.path.join(video_dir, f"{video_name}_with_subtitles{video_ext}")

    # 确保输出目录存在
    ensure_directory_exists(os.path.dirname(output_path))

    # 获取字幕扩展名
    subtitle_ext = os.path.splitext(subtitle_path)[1].lower()

    if subtitle_ext not in ['.srt', '.ass', '.ssa']:
        logger.error(f"不支持的字幕格式: {subtitle_ext}，请使用.srt或.ass格式")
        return None

    # 处理路径中的特殊字符（对所有系统统一处理）
    escaped_subtitle_path = subtitle_path.replace("'", "\\'").replace(":", "\\:")

    try:
        if subtitle_ext == '.ass' or subtitle_ext == '.ssa':
            # ASS/SSA格式字幕，直接使用subtitles滤镜
            cmd = [
                'ffmpeg', '-y', '-i', video_path,
                '-vf', f"subtitles='{escaped_subtitle_path}'",
                '-c:a', 'copy',
                output_path
            ]
        else:
            # SRT格式字幕，使用force_style参数
            if position == 'bottom':
                style_params = f"subtitles='{escaped_subtitle_path}':force_style='FontName=Arial,FontSize={font_size},PrimaryColour=&H{font_color},OutlineColour=&H{border_color},BorderStyle=1,Outline={border_width},MarginV={bottom_margin}'"
            elif position == 'top':
                style_params = f"subtitles='{escaped_subtitle_path}':force_style='FontName=Arial,FontSize={font_size},PrimaryColour=&H{font_color},OutlineColour=&H{border_color},BorderStyle=1,Outline={border_width},Alignment=6,MarginV={top_margin}'"
            else:  # middle
                style_params = f"subtitles='{escaped_subtitle_path}':force_style='FontName=Arial,FontSize={font_size},PrimaryColour=&H{font_color},OutlineColour=&H{border_color},BorderStyle=1,Outline={border_width},Alignment=8'"

            cmd = [
                'ffmpeg', '-y', '-i', video_path,
                '-vf', style_params,
                '-c:a', 'copy',
                output_path
            ]

        # 执行命令
        result = subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        logger.debug(f"成功添加字幕: {output_path}")
        return output_path

    except subprocess.CalledProcessError as e:
        logger.error(f"添加字幕失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return None


def process_video_with_audio_and_subtitles(video_data: Dict[str, Any], output_dir: str,
                                          temp_dir: str, subtitle_settings: Dict[str, Any] = None) -> Optional[str]:
    """
    处理单个视频条目，合并音频并添加高级字幕

    参数:
        video_data: 视频数据字典，包含视频、音频、字幕和ASR相关信息
        output_dir: 输出目录
        temp_dir: 临时文件目录
        subtitle_settings: 字幕设置参数字典

    返回:
        Optional[str]: 处理成功返回最终输出视频路径，失败返回None
    """
    # 确保目录存在
    ensure_directory_exists(output_dir)
    ensure_directory_exists(temp_dir)

    # 视频和音频路径 - 转换为绝对路径
    video_path = os.path.abspath(video_data["clip_video"])
    audio_path = os.path.abspath(video_data["replace_audio"])

    # 检查文件是否存在
    if not os.path.exists(video_path):
        logger.error(f"错误: 视频文件不存在: {video_path}")
        logger.error(f"原始路径: {video_data['clip_video']}")
        return None

    if not os.path.exists(audio_path):
        logger.error(f"错误: 音频文件不存在: {audio_path}")
        logger.error(f"原始路径: {video_data['replace_audio']}")
        return None

    # 获取输出文件名
    base_filename = os.path.basename(video_path)
    base_name = os.path.splitext(base_filename)[0]

    logger.debug(f"处理视频: {base_name}")
    logger.debug(f"  - 视频路径: {video_path}")
    logger.debug(f"  - 音频路径: {audio_path}")

    # 检查是否有ASR数据和目标词汇
    has_asr_data = 'asr_words' in video_data and video_data['asr_words']
    has_target_words = 'word_zh' in video_data and 'word_en' in video_data

    if has_asr_data and has_target_words:
        logger.debug(f"  - 目标词汇: {video_data['word_zh']} -> {video_data['word_en']}")

    # 临时合并音视频的输出路径
    temp_merged_path = os.path.join(temp_dir, f"{base_name}_merged.mp4")

    # 最终输出路径
    final_output_path = os.path.join(output_dir, f"{base_name}.mp4")

    # 创建字幕文件路径（使用ASS格式以支持高级功能）
    subtitle_path = os.path.join(temp_dir, f"{base_name}.ass")

    logger.debug(f"  - 字幕路径: {subtitle_path}")
    logger.debug(f"  - 临时合并路径: {temp_merged_path}")
    logger.debug(f"  - 最终输出路径: {final_output_path}")

    # 创建高级字幕文件
    try:
        if has_asr_data and has_target_words:
            # 使用高级字幕创建功能
            create_advanced_subtitle_file(video_data, subtitle_path)
            logger.debug("  - 使用高级字幕（支持目标词汇高亮）")
        else:
            # 使用简单字幕创建功能
            create_simple_subtitle_file(video_data.get("text", ""), subtitle_path)
            logger.debug("  - 使用简单字幕")
    except Exception as e:
        logger.error(f"创建字幕文件失败: {e}")
        return None

    # 验证字幕文件创建成功
    if not os.path.exists(subtitle_path):
        logger.error(f"错误: 创建字幕文件失败: {subtitle_path}")
        return None

    # 首先合并视频和音频
    if not merge_video_audio(video_path, audio_path, temp_merged_path):
        logger.error(f"处理失败: 无法合并视频和音频 - {base_filename}")
        return None

    # 验证临时合并文件创建成功
    if not os.path.exists(temp_merged_path):
        logger.error(f"错误: 临时合并文件不存在: {temp_merged_path}")
        return None

    # 设置默认字幕参数
    if subtitle_settings is None:
        subtitle_settings = {
            "font_size": 28,
            "font_color": "FFFFFF",  # 白色
            "border_color": "000000",  # 黑色
            "border_width": 2.0,
            "position": "bottom",
            "bottom_margin": 30,
            "top_margin": 10
        }

    # 为合并后的视频添加字幕
    final_video = add_subtitles_to_video(
        temp_merged_path,
        subtitle_path,
        final_output_path,
        **subtitle_settings
    )

    if final_video:
        logger.info(f"处理成功: {final_output_path}")
        return final_output_path
    else:
        logger.warning(f"处理失败: 无法添加字幕 - {base_filename}")
        return None


def batch_process_videos_from_json(json_file: str, output_dir: str, temp_dir: str = None,
                                  subtitle_settings: Dict[str, Any] = None) -> List[str]:
    """
    从JSON文件中批量处理视频

    参数:
        json_file: JSON文件路径，包含视频列表
        output_dir: 输出目录
        temp_dir: 临时文件目录，默认为output_dir/temp
        subtitle_settings: 字幕设置

    返回:
        List[str]: 成功处理的视频路径列表
    """
    # 读取JSON文件
    with open(json_file, 'r', encoding='utf-8') as f:
        video_data_list = json.load(f)

    # 设置临时目录
    if temp_dir is None:
        temp_dir = os.path.join(output_dir, "temp")

    # 确保目录存在
    ensure_directory_exists(output_dir)
    ensure_directory_exists(temp_dir)

    # 处理所有视频
    successful_outputs = []
    total_videos = len(video_data_list)

    logger.debug(f"\n开始处理 {total_videos} 个视频...")

    for index, video_data in enumerate(video_data_list):
        logger.debug(f"\n[{index+1}/{total_videos}] 正在处理视频: {video_data.get('video', 'Unknown')} - {video_data.get('timestamp', 'Unknown')}")

        # 检查必要字段是否存在
        required_fields = ['clip_video', 'replace_audio', 'text', 'timestamp']
        missing_fields = [field for field in required_fields if field not in video_data]

        if missing_fields:
            logger.warning(f"错误: 数据缺少必要字段 {', '.join(missing_fields)}")
            continue

        # 确保路径存在
        if not os.path.exists(video_data['clip_video']):
            # 尝试解析相对路径
            base_dir = os.path.dirname(json_file)
            video_data['clip_video'] = os.path.join(base_dir, video_data['clip_video'])
            logger.debug(f"尝试使用相对路径: {video_data['clip_video']}")

        if not os.path.exists(video_data['replace_audio']):
            # 尝试解析相对路径
            base_dir = os.path.dirname(json_file)
            video_data['replace_audio'] = os.path.join(base_dir, video_data['replace_audio'])
            logger.debug(f"尝试使用相对路径: {video_data['replace_audio']}")

        # 处理视频
        output_path = process_video_with_audio_and_subtitles(
            video_data,
            output_dir,
            temp_dir,
            subtitle_settings
        )

        if output_path:
            successful_outputs.append(output_path)
            logger.debug(f"[{index+1}/{total_videos}] 处理成功")
        else:
            logger.debug(f"[{index+1}/{total_videos}] 处理失败")

    logger.debug(f"\n成功处理 {len(successful_outputs)}/{total_videos} 个视频")
    return successful_outputs


if __name__ == '__main__':
    # 设置JSON文件路径
    json_file = 'rewrite_v2.json'

    # 设置输出目录
    output_dir = 'output/final_videos'
    temp_dir = 'output/final_videos/temp'

    # 获取当前工作目录
    current_dir = os.getcwd()
    logger.debug(f"当前工作目录: {current_dir}")

    # 转换为绝对路径
    json_file = os.path.abspath(json_file)
    output_dir = os.path.abspath(output_dir)
    temp_dir = os.path.abspath(temp_dir)

    logger.debug(f"JSON文件路径: {json_file}")
    logger.debug(f"输出目录: {output_dir}")
    logger.debug(f"临时目录: {temp_dir}")

    # 确保目录存在
    ensure_directory_exists(output_dir)
    ensure_directory_exists(temp_dir)

    # 设置字幕参数
    subtitle_settings = {
        "font_size": 28,
        "font_color": "FFFFFF",  # 白色
        "border_color": "000000",  # 黑色
        "border_width": 2.0,
        "position": "bottom",
        "bottom_margin": 30,
        "top_margin": 10
    }

    # 检查JSON文件是否存在
    if not os.path.exists(json_file):
        logger.debug(f"错误: JSON文件不存在: {json_file}")
        exit(1)

    # 打印一些调试信息
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            video_data_list = json.load(f)
            logger.debug(f"成功读取JSON文件，包含 {len(video_data_list)} 条视频数据")

            # 打印第一条数据作为示例
            if video_data_list:
                logger.debug(f"示例数据: {json.dumps(video_data_list[0], ensure_ascii=False, indent=2)}")
    except Exception as e:
        logger.debug(f"读取JSON文件时出错: {e}")
        exit(1)

    # 批量处理视频
    successful_videos = batch_process_videos_from_json(
        json_file,
        output_dir,
        temp_dir,
        subtitle_settings=subtitle_settings
    )

    # 打印处理结果
    logger.debug("\n处理结果摘要:")
    logger.debug(f"总共处理: {len(successful_videos)} 个视频")
    for i, video in enumerate(successful_videos):
        logger.debug(f"{i+1}. {video}")

    if successful_videos:
        logger.debug(f"\n处理完成! 所有视频已保存到: {output_dir}")
    else:
        logger.debug("\n警告: 没有成功处理任何视频!")

    # 清空临时目录
    shutil.rmtree(temp_dir)
