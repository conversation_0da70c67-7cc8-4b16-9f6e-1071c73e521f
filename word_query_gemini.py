#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project: tv_en_learning
@File   : 基于大模型进行匹配
<AUTHOR> 小林同学
@Date   : 2025/5/20 17:31
'''
import os
import json
import time
from gemini import chat, chat_v2


def gemini_query(srt_path: str, word: str)-> list:
    """
    使用Gemini模型查询字幕文件中的特定词语

    参数:
        srt_path: 字幕文件路径
        word: 要查询的词语

    返回:
        包含查询结果的列表

    异常:
        可能抛出请求异常或JSON解析异常
    """
    # apikey = "sk-narra-ZPA07ethehTb2VQ5mMI1P8tt"    # 我的负载均衡 gemini
    apikey = "AIzaSyB9nxsAvlZ1FKtPVoG8YJATht6cCTVMBC4"
    model = "gemini-2.0-flash"

    # 读取字幕文件内容
    try:
        with open(srt_path, "r", encoding='utf-8') as f:
            srt_content = f.read()
    except Exception as e:
        raise Exception(f"读取字幕文件失败: {str(e)}")

    messages = [
        {"role": "system",
         "content": "你是一个精通自然语言处理和字幕分析的AI助手，特别强调执行指令的精确性和字面性。"},
        {"role": "user", "content": """
# 任务

你的核心任务是分析用户提供的SRT字幕文件内容，在其中**绝对精准地查找**用户指定的中文词语。这意味着只有当字幕文本**字面上、逐字地包含**目标词语时，才算作匹配。在找到精准匹配的字幕段后，你需要结合其上下文理解该词语的真实含义，并尝试判断说话人的一致性。最终，你需要以JSON格式输出所有**严格符合匹配要求**的结果。

# 输入

1.  **SRT字幕内容 (srt_content)**: 一个包含完整SRT字幕格式的文本字符串。
2.  **目标中文词语 (target_word)**: 用户希望在字幕中查找的特定中文词语。
3.  **上下文窗口大小 (context_window_size)**: 一个整数，表示在找到目标词语的字幕段前后，分别取多少条字幕作为上下文。

# 处理要求

1.  **SRT解析**: 正确解析SRT格式，提取每条字幕的序号、时间戳和文本内容。

2.  **核心匹配原则：字面包含 (CRUCIAL & NON-NEGOTIABLE)**:
    *   **首要步骤**: 对于SRT中的每一条字幕文本，**首先且必须**检查该文本是否**字面上、逐字地、完整地包含**用户提供的 `target_word`。
    *   **严格过滤**: **只有当且仅当**字幕文本**确实包含** `target_word` 时，该字幕段才能被视为一个有效的“匹配字幕段 (matched_segment)”。
    *   **排除**: 如果字幕文本虽然语义相关，但**没有字面上包含** `target_word`，则**绝对不能**将该字幕段视为匹配项，**不得**将其纳入后续分析或最终输出。

3.  **上下文提取 (仅对字面匹配的字幕段进行)**: 对于每个**成功通过上述字面匹配校验**的字幕段：
    *   记录该字幕段本身。
    *   根据 `context_window_size` 提取其前N条字幕作为“前文 (context_before)”。
    *   根据 `context_window_size` 提取其后N条字幕作为“后文 (context_after)”。

4.  **语义理解 (仅对字面匹配的字幕段进行)**: 分析 `target_word` 在其所在字幕段及上下文中的具体含义。

5.  **说话人判断 (尝试性，仅对字面匹配的字幕段进行)**: 基于对话的连贯性、内容主题的延续性等线索，尝试判断包含 `target_word` 的字幕段及其直接上下文是否可能为同一说话人。说明判断依据。

6.  **最终输出校验**: 在生成JSON输出前，**再次确认**所有 `matched_segment` 中的 `text` 字段都**明确无误地包含** `target_word`。这是最后的防线。

# 输出格式 (JSON)

请将所有找到的、**严格符合字面匹配要求**的匹配结果组织成一个JSON数组。数组中的每个元素是一个对象，结构如下：

```json
[
  {
    "target_word": "目标中文词语",
    "matched_segment": { // 此处 text 字段必须包含 target_word
      "index": "字幕序号 (字符串)",
      "timestamp": "时间戳 (字符串)",
      "text": "包含目标词语的字幕文本 (字符串)"
    },
    "context_before": [
      // ...
    ],
    "context_after": [
      // ...
    ],
    "semantic_analysis": "对目标词语在此上下文中真实含义的分析描述 (字符串)",
    "speaker_consistency_analysis": {
        "assessment": "可能同一说话人 / 可能不同说话人 / 难以判断 (字符串)",
        "reasoning": "支持该判断的理由 (字符串)"
    }
  }
  // ... 其他严格匹配项
]
```
如果未找到任何**字面上包含** `target_word` 的字幕段，请返回一个空数组 `[]`。

# 示例（假设 `target_word` 为 "大模型", `context_window_size` 为 1）

**输入 (srt_content):**
```srt
1
00:00:01,000 --> 00:00:03,500
大家好，今天我们聊聊人工智能。

2
00:00:04,000 --> 00:00:06,800
特别是关于“大模型”的最新进展。

3
00:00:07,200 --> 00:00:09,500
这个AI模型能力很强。

4
00:00:10,000 --> 00:00:12,000
它能解决很多复杂问题。
```

**预期输出 (JSON):**
```json
[
  {
    "target_word": "大模型",
    "matched_segment": {
      "index": "2",
      "timestamp": "00:00:04,000 --> 00:00:06,800",
      "text": "特别是关于“大模型”的最新进展。" // 明确包含 "大模型"
    },
    "context_before": [
      {
        "index": "1",
        "timestamp": "00:00:01,000 --> 00:00:03,500",
        "text": "大家好，今天我们聊聊人工智能。"
      }
    ],
    "context_after": [
      {
        "index": "3",
        "timestamp": "00:00:07,200 --> 00:00:09,500",
        "text": "这个AI模型能力很强。"
      }
    ],
    "semantic_analysis": "在此处，“大模型”特指具有大规模参数的先进人工智能模型，讨论的是其技术发展情况。",
    "speaker_consistency_analysis": {
        "assessment": "可能同一说话人",
        "reasoning": "字幕2是对字幕1引入话题的细化，内容连贯，表述方式一致。"
    }
  }
  // 注意：字幕3虽然语义上和“大模型”相关，但因字面上不包含“大模型”，所以不应出现在结果中。
]
```

# 开始执行

请根据以下提供的实际输入执行任务：

**SRT字幕内容 (srt_content):**
```srt
%s
```

**目标中文词语 (target_word):**
%s

**上下文窗口大小 (context_window_size):**
2

注意不要输出除结果以外的任何其他字符串！
""" % (srt_content, word)},
    ]

    try:
        # 调用API
        res = chat_v2(apikey, model, messages, '0.7')

        # 处理返回结果
        if not res or 'content' not in res:
            raise Exception("API返回结果为空或格式不正确")

        content = res.get('content', '')
        if not content:
            raise Exception("API返回内容为空")

        # 清理JSON格式
        content = content.replace("```json", "").replace("```", "")

        # 解析JSON
        try:
            json_data = json.loads(content)
            if not isinstance(json_data, list):
                raise Exception(f"返回结果不是列表格式: {content[:10]}...")
            return json_data
        except json.JSONDecodeError as e:
            raise Exception(f"JSON解析失败: {str(e)}，内容: {content[:10]}...")

    except Exception as e:
        # 捕获所有异常并重新抛出，便于上层函数进行重试
        raise Exception(f"查询失败: {str(e)}")


def load_words(word_file_path: str, limit: int = None):
    """
    从JSON文件中加载单词列表

    参数:
        word_file_path: 单词文件路径
        limit: 限制加载的单词数量，默认为None表示加载所有单词

    返回:
        单词列表
    """
    try:
        with open(word_file_path, "r", encoding='utf-8') as f:
            words_data = json.load(f)

        # 如果指定了限制数量，则只返回前limit个单词
        if limit is not None and limit > 0:
            words_data = words_data[:limit]

        return words_data
    except Exception as e:
        print(f"加载单词文件失败: {str(e)}")
        return []


def run(srt_dir: str, word: str, max_retries: int = 5, retry_delay: int = 30, output_dir: str = None):
    """
    运行字幕查询，支持失败重试功能

    参数:
        srt_dir: 字幕文件目录
        word: 要查询的词语
        max_retries: 最大重试次数，默认3次
        retry_delay: 重试间隔时间(秒)，默认3秒
        output_dir: 输出目录，默认为None时使用resources/word_search

    返回:
        所有查询结果的列表
    """
    srt_path_list = os.listdir(srt_dir)
    origin_dir = '/Users/<USER>/Desktop/home/<USER>/resources/srt/zh/'
    srt_path_list = [os.path.join(origin_dir, i) for i in srt_path_list]
    # 按照名称排序
    srt_path_list.sort()
    all_res = []

    for srt_path in srt_path_list:
        print(f"# 开始处理字幕文件: {srt_path}")

        # 添加重试逻辑
        retry_count = 0
        success = False

        while retry_count < max_retries and not success:
            try:
                if retry_count > 0:
                    print(f"第 {retry_count + 1} 次重试查询: {srt_path}")

                res = gemini_query(srt_path, word)
                if not res:
                    print(f"    - 未找到任何关于 '{word}' 的匹配结果 ---> 准备处理下一个字幕")
                    success = True
                    continue
                res = [i for i in res if word in i['matched_segment']['text']]
                # 为 res 中每个原始新增一个字段，值为 srt_path
                for i in res:
                    i['srt_file'] = srt_path
                all_res += res
                success = True
                print(f"成功查询到 {len(res)} 个结果")

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"查询失败: {str(e)}，{retry_count}/{max_retries} 次重试，等待 {retry_delay} 秒...")
                    time.sleep(retry_delay)
                else:
                    print(f"重试 {max_retries} 次后仍然失败: {str(e)}，跳过当前记录")

    # 不再在这里保存结果，而是在上层函数中保存
    if all_res:
        print(f"成功找到 {len(all_res)} 个关于 '{word}' 的匹配结果")
    else:
        print(f"未找到任何关于 '{word}' 的匹配结果")

    return all_res


def process_words_from_file(word_file_path: str, srt_dir: str, limit: int = 10, max_retries: int = 5, retry_delay: int = 30):
    """
    从指定文件中加载单词并处理

    参数:
        word_file_path: 单词文件路径
        srt_dir: 字幕文件目录
        limit: 限制处理的单词数量，默认为10
        max_retries: 最大重试次数，默认3次
        retry_delay: 重试间隔时间(秒)，默认3秒
        mock_mode: 是否使用模拟模式，默认为True
    """
    # 加载单词列表
    words_data = load_words(word_file_path, limit)

    if not words_data:
        print(f"从{word_file_path}加载单词失败或文件为空")
        return

    print(f"已加载{len(words_data)}个单词，开始处理...")

    # 创建输出目录
    output_dir = "resources/word_test"
    os.makedirs(output_dir, exist_ok=True)

    # 处理每个单词
    for i, word_data in enumerate(words_data):
        word = word_data.get('word')
        word_type = word_data.get('translations', [{}])[0].get('type', '')
        # 获取中文翻译，用于查询
        chinese_word = word_data.get('translations', [{}])[0].get('translation', '')

        print(f"\n[{i+1}/{len(words_data)}] 开始处理单词: {word} ({word_type}) -> 中文: {chinese_word}")

        # 运行查询，使用中文翻译进行搜索
        try:
            results = run(srt_dir, chinese_word, max_retries, retry_delay, output_dir)
        except Exception as e:
            print(f"处理单词 {word} 时发生错误: {str(e)}")
            results = []

        # 将结果保存到以英文单词命名的文件中
        output_file = os.path.join(output_dir, f"{word}.json")
        with open(output_file, "w", encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(results)} 个查询结果到文件: {output_file}")

        # 打印分隔线
        print("-" * 50)


if __name__ == '__main__':
    # 从指定文件中加载单词并处理前10个
    word_file_path = "resources/word/CET6luan_1trans_len_8_10.json"
    srt_dir = "resources/srt/zh"

    # 处理单词，使用模拟模式避免API调用问题
    process_words_from_file(word_file_path, srt_dir, limit=10)

    print("\n\n处理完成！结果已保存到 resources/word_test 目录")
